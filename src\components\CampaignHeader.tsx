import { Card, CardContent } from '@/components/ui/card';
import React from 'react';
import { Calendar, Users } from 'lucide-react';

interface CampaignHeaderProps {
  title?: string;
  subtitle?: string;
  period?: string;
  profilesCount?: number | string;
}

const CampaignHeader: React.FC<CampaignHeaderProps> = ({
  title = 'Dia dos Namorados (O Boticário)',
  subtitle = 'RELATÓRIO CAMPANHA',
  period = '12/05/2025 até 12/06/2025',
  profilesCount = 23,
}) => (
  <div className="mb-8">
    <div className="mb-4">
      <div className="text-sm text-gray-400 mb-1">{subtitle}</div>
      <h1 className="text-2xl font-bold">{title}</h1>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
      <Card className="bg-white text-black">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600 mb-1 flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            Período da duração da campanha
          </div>
          <div className="font-semibold">{period}</div>
        </CardContent>
      </Card>
      <Card className="bg-white text-black">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600 mb-1 flex items-center gap-1">
            <Users className="w-4 h-4" />
            Quantidade de perfis participantes
          </div>
          <div className="font-semibold text-2xl">{profilesCount}</div>
        </CardContent>
      </Card>
    </div>
  </div>
);

export default CampaignHeader; 
import { CampaignData, InfluencersData } from '@/types/api';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export async function fetchCampaignData(): Promise<CampaignData> {
  const res = await fetch(`${API_BASE_URL}/campaigns/2fb65748-6ee7-4005-b8ba-a31e1adc8f66/report`);
  if (!res.ok) throw new Error('Erro ao buscar dados da campanha');
  return res.json();
}

export async function fetchInfluencersData(): Promise<InfluencersData> {
  const res = await fetch(`${API_BASE_URL}/campaigns/2fb65748-6ee7-4005-b8ba-a31e1adc8f66/report/social-networks`);
  if (!res.ok) throw new Error('Erro ao buscar dados dos influencers');
  return res.json();
} 
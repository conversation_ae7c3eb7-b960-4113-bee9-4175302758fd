@import 'tailwindcss';

/* Você pode adicionar estilos globais a<PERSON>o, se quiser */

/* Configuração base para dark mode */
body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

@theme {
    /* Configurações para dark mode */
    --color-background: light-dark(#ffffff, #0a0a0a);
    --color-foreground: light-dark(#0a0a0a, #fafafa);
    --color-card: light-dark(#ffffff, #0a0a0a);
    --color-card-foreground: light-dark(#0a0a0a, #fafafa);
    --color-popover: light-dark(#ffffff, #0a0a0a);
    --color-popover-foreground: light-dark(#0a0a0a, #fafafa);
    --color-muted: light-dark(#f5f5f5, #262626);
    --color-muted-foreground: light-dark(#737373, #a3a3a3);
    --color-accent: light-dark(#f5f5f5, #262626);
    --color-accent-foreground: light-dark(#0a0a0a, #fafafa);
    --color-border: light-dark(#e5e5e5, #262626);
    --color-input: light-dark(#e5e5e5, #262626);
    --color-ring: light-dark(#0a0a0a, #d4d4d4);

    /* Cores primárias */
    --color-primary-950: #243300;
    --color-primary-900: #475B0C;
    --color-primary-800: #546C08;
    --color-primary-700: #6A8902;
    --color-primary-600: #8DB500;
    --color-primary-500: #CEFC00;
    --color-primary-400: #D4F820;
    --color-primary-300: #EEFF53;
    --color-primary-200: #F2FF92;
    --color-primary-100: #FAFFC5;
    --color-primary-50: #FDFEF4;
  
    /* Cores secundárias */
    --color-secondary-950: #400C46;
    --color-secondary-900: #632468;
    --color-secondary-800: #762583;
    --color-secondary-700: #8E2C9F;
    --color-secondary-600: #AA3BC1;
    --color-secondary-500: #C252DC;
    --color-secondary-400: #D88EEC;
    --color-secondary-300: #E6B4F3;
    --color-secondary-200: #F0D5F9;
    --color-secondary-100: #F8EAFD;
    --color-secondary-50: #FBF5FE;
  
    /* Cores terciárias */
    --color-tertiary-950: #3F006C;
    --color-tertiary-900: #5D1390;
    --color-tertiary-800: #711584;
    --color-tertiary-700: #8513DD;
    --color-tertiary-600: #9E2CFA;
    --color-tertiary-500: #AD47FF;
    --color-tertiary-400: #C47AFF;
    --color-tertiary-300: #DAAEFF;
    --color-tertiary-200: #EAD1FF;
    --color-tertiary-100: #F4E6FF;
    --color-tertiary-50: #FAF4FF;
  
    /* Cores neutras */
    --color-neutral-950: #0A0A0A;
    --color-neutral-900: #171717;
    --color-neutral-800: #262626;
    --color-neutral-700: #404040;
    --color-neutral-600: #525252;
    --color-neutral-500: #737373;
    --color-neutral-400: #A3A3A3;
    --color-neutral-300: #D4D4D4;
    --color-neutral-200: #E5E5E5;
    --color-neutral-100: #F5F5F5;
    --color-neutral-50: #FAFAFA;
    --color-neutral-black: #000000;
    --color-neutral-white: #FFFFFF;
  
    /* Cores de perigo */
    --color-danger-600: #DC2626;
    --color-danger-300: #FCA5A5;
    --color-danger-100: #FEE2E2;
  
    /* Cores de aviso */
    --color-warning-400: #FAC515;
    --color-warning-200: #FF0BA;
    --color-warning-50: #FFECB8;
  
    /* Cor de marca */
    --color-brand: #123456;
  }
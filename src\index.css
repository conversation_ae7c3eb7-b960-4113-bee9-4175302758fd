@import 'tailwindcss';

/* Você pode adicionar estilos globais a<PERSON>o, se quiser */

/* Configuração base para dark mode */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme {
    /* Cores primárias */
    --color-primary-950: #243300;
    --color-primary-900: #475B0C;
    --color-primary-800: #546C08;
    --color-primary-700: #6A8902;
    --color-primary-600: #8DB500;
    --color-primary-500: #CEFC00;
    --color-primary-400: #D4F820;
    --color-primary-300: #EEFF53;
    --color-primary-200: #F2FF92;
    --color-primary-100: #FAFFC5;
    --color-primary-50: #FDFEF4;
  
    /* Cores secundárias */
    --color-secondary-950: #400C46;
    --color-secondary-900: #632468;
    --color-secondary-800: #762583;
    --color-secondary-700: #8E2C9F;
    --color-secondary-600: #AA3BC1;
    --color-secondary-500: #C252DC;
    --color-secondary-400: #D88EEC;
    --color-secondary-300: #E6B4F3;
    --color-secondary-200: #F0D5F9;
    --color-secondary-100: #F8EAFD;
    --color-secondary-50: #FBF5FE;
  
    /* Cores terciárias */
    --color-tertiary-950: #3F006C;
    --color-tertiary-900: #5D1390;
    --color-tertiary-800: #711584;
    --color-tertiary-700: #8513DD;
    --color-tertiary-600: #9E2CFA;
    --color-tertiary-500: #AD47FF;
    --color-tertiary-400: #C47AFF;
    --color-tertiary-300: #DAAEFF;
    --color-tertiary-200: #EAD1FF;
    --color-tertiary-100: #F4E6FF;
    --color-tertiary-50: #FAF4FF;
  
    /* Cores neutras */
    --color-neutral-950: #0A0A0A;
    --color-neutral-900: #171717;
    --color-neutral-800: #262626;
    --color-neutral-700: #404040;
    --color-neutral-600: #525252;
    --color-neutral-500: #737373;
    --color-neutral-400: #A3A3A3;
    --color-neutral-300: #D4D4D4;
    --color-neutral-200: #E5E5E5;
    --color-neutral-100: #F5F5F5;
    --color-neutral-50: #FAFAFA;
    --color-neutral-black: #000000;
    --color-neutral-white: #FFFFFF;
  
    /* Cores de perigo */
    --color-danger-600: #DC2626;
    --color-danger-300: #FCA5A5;
    --color-danger-100: #FEE2E2;
  
    /* Cores de aviso */
    --color-warning-400: #FAC515;
    --color-warning-200: #FF0BA;
    --color-warning-50: #FFECB8;
  
    /* Cor de marca */
    --color-brand: #123456;
  }
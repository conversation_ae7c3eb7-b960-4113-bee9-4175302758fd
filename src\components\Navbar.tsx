import React from 'react';
import { ThemeToggle } from './theme-toggle';

const Navbar: React.FC = () => (
  <div className="bg-white dark:bg-neutral-900 border-b border-neutral-200 dark:border-neutral-700 mb-8">
    <nav className="flex items-center justify-between gap-2 px-4 py-2">
      <img src="/logo.svg" alt="Hype Logo" className="h-18 w-auto" />
      <ThemeToggle />
    </nav>
  </div>
);

export default Navbar;



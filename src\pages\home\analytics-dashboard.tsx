'use client';

import {
  Search,
  Eye,
  Heart,
  MessageCircle,
  Venus,
  Mars,
  Instagram,
  Youtube,
  Music,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import CampaignHeader from '@/components/CampaignHeader';
import Navbar from '@/components/Navbar';
import { useQuery } from '@tanstack/react-query';
import { fetchCampaignData, fetchInfluencersData } from '@/APIs/analytics';
import { CampaignData, InfluencersData, SocialNetwork, InfluencerItem } from '@/types/api';
import React, { useState } from 'react';

// Mapeamento dos tipos de rede social para nome, cor e ícone
const socialNetworkMap: {
  [key: number]: {
    name: string;
    icon: React.ReactNode;
    bgColor: string;
  };
} = {
  1: {
    name: 'Instagram',
    icon: <Instagram className="w-5 h-5" />,
    bgColor: 'bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400',
  },
  2: {
    name: 'Facebook Page',
    icon: <Instagram className="w-5 h-5" />, // Substitua pelo ícone correto se necessário
    bgColor: 'bg-blue-600',
  },
  3: {
    name: 'Facebook Group',
    icon: <Instagram className="w-5 h-5" />, // Substitua pelo ícone correto se necessário
    bgColor: 'bg-blue-800',
  },
  4: {
    name: 'TikTok',
    icon: <Music className="w-5 h-5" />,
    bgColor: 'bg-black',
  },
  5: {
    name: 'YouTube',
    icon: <Youtube className="w-5 h-5" />,
    bgColor: 'bg-red-600',
  },
};

export default function Component() {
  // Fetch dos dados gerais da campanha
  const { data: campaignData } = useQuery<CampaignData>({
    queryKey: ['campaign-report'],
    queryFn: fetchCampaignData,
  });

  // Fetch dos dados por perfil (influencers)
  const { data: influencersData } = useQuery<InfluencersData>({
    queryKey: ['campaign-report-influencers'],
    queryFn: fetchInfluencersData,
  });

  // Social platforms breakdown
  const socialPlatforms = campaignData?.by_social_network?.map((platform: SocialNetwork) => ({
    name: socialNetworkMap[platform.type]?.name || platform.name,
    icon: socialNetworkMap[platform.type]?.icon,
    bgColor: socialNetworkMap[platform.type]?.bgColor,
    stats: {
      women: `${platform.genders.female}% mulheres`,
      men: `${platform.genders.male}% homens`,
      engagement: {
        likes: platform.likes_count,
        comments: platform.comments_count,
        shares: platform.views_count, // Não há shares, usar views_count como exemplo
      },
    },
  })) || [];

  // Influencers table
  const influencers = influencersData?.data?.map((item: InfluencerItem) => ({
    name: item.influencer_name,
    platform: socialNetworkMap[item.social_network_type]?.name?.toLowerCase() || 'desconhecido',
    platformType: item.social_network_type,
    verified: false, // Não há campo de verificado
    views: item.views_count,
    likes: item.likes_count,
    comments: item.comments_count,
    womenPercent: `${item.genders.female}%`,
    menPercent: `${item.genders.male}%`,
    photo: item.influencer_photo,
    postLink: item.post_link,
  })) || [];

  // Totais gerais
  const totalViews = campaignData?.views_count || 0;
  const totalLikes = campaignData?.likes_count || 0;
  const totalComments = campaignData?.comments_count || 0;
  const totalGenders = campaignData?.genders || { female: 0, male: 0 };
  const profilesCount =
    typeof influencersData?.total === 'number'
      ? influencersData.total
      : 'não informado';
  const period =
    campaignData?.start_date && campaignData?.end_date
      ? `${campaignData.start_date} até ${campaignData.end_date}`
      : 'não informado';

  const platformIcons: Record<string, React.ReactNode> = {
    instagram: <Instagram className="w-5 h-5" />,
    tiktok: <Music className="w-5 h-5" />,
    youtube: <Youtube className="w-5 h-5" />,
  };

  const platformBg: Record<string, string> = {
    instagram: 'bg-gradient-to-br from-purple-500 via-pink-500 to-orange-400',
    tiktok: 'bg-black',
    youtube: 'bg-red-600',
  };

  const title = campaignData?.name || 'TITULO DE CAMPANHA NÃO INFORMADO';

  // Estados para filtros
  const [search, setSearch] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState('all');
  const [orderBy, setOrderBy] = useState('views');

  // Função de filtro e ordenação
  const filteredInfluencers = influencers
    .filter((influencer) => {
      const matchesSearch = influencer.name
        .toLowerCase()
        .includes(search.toLowerCase());
      const matchesNetwork =
        selectedNetwork === 'all' || influencer.platform === selectedNetwork;
      return matchesSearch && matchesNetwork;
    })
    .sort((a, b) => {
      if (orderBy === 'views') {
        return Number(b.views) - Number(a.views);
      } else if (orderBy === 'likes') {
        return Number(b.likes) - Number(a.likes);
      } else if (orderBy === 'comments') {
        return Number(b.comments) - Number(a.comments);
      }
      return 0;
    });

  return (
    <>
      <div className="min-h-screen bg-white dark:bg-neutral-900 text-black dark:text-white p-6">
        {/* Header */}
        <Navbar />

        <CampaignHeader title={title} profilesCount={profilesCount} period={period} />

        {/* General Stats */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Geral</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-white dark:bg-neutral-800 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <Eye className="w-4 h-4" />
                  <span>Visualizações</span>
                </div>
                <div className="text-2xl font-bold">{totalViews}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-800 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <Heart className="w-4 h-4" />
                  <span>Curtidas</span>
                </div>
                <div className="text-2xl font-bold">{totalLikes}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-800 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Comentários</span>
                </div>
                <div className="text-2xl font-bold">{totalComments}</div>
              </CardContent>
            </Card>
            <Card className="bg-white dark:bg-neutral-800 text-black dark:text-white">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <Venus className="w-4 h-4 text-pink-400" />
                  <Mars className="w-4 h-4 text-blue-400" />
                  <span>Gênero</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-400 to-blue-400 flex items-center justify-center relative">
                    <div className="w-12 h-12 bg-white rounded-full"></div>
                    <div
                      className="absolute inset-0 rounded-full"
                      style={{
                        background: `conic-gradient(#ec4899 0deg ${69.5 * 3.6}deg, #3b82f6 ${69.5 * 3.6}deg 360deg)`,
                      }}
                    ></div>
                  </div>
                  <div className="text-xs">
                    <div className="flex items-center gap-1 mb-1">
                      <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                      <span>{totalGenders.female}% mulheres</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span>{totalGenders.male}% homens</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Social Media Breakdown */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Por Rede Social</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {socialPlatforms.map((platform, index) => (
              <Card key={index} className="bg-white dark:bg-neutral-800 text-black dark:text-white">
                <CardContent className="p-4">
                  <div
                    className={`w-8 h-8 ${platform.bgColor} rounded-lg flex items-center justify-center text-white mb-3`}
                  >
                    {platform.icon}
                  </div>
                  <div className="mb-3">
                    <div className="flex items-center gap-1 mb-1 text-sm">
                      <Venus className="w-3 h-3 text-pink-400" />
                      <span>{platform.stats.women}</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <Mars className="w-3 h-3 text-blue-400" />
                      <span>{platform.stats.men}</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                    <span>
                      <Heart className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.likes}
                    </span>
                    <span>
                      <MessageCircle className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.comments}
                    </span>
                    <span>
                      <Eye className="inline w-4 h-4" />{' '}
                      {platform.stats.engagement.shares}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Influencer Performance Table */}
        <div className="text-black dark:text-white">
          <h2 className="text-lg font-semibold mb-4">Por Perfil</h2>

          <div className="bg-white dark:bg-neutral-800 rounded-lg p-4">
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row gap-4 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar influencer"
                  className="pl-10 bg-white dark:bg-neutral-700 text-black dark:text-white"
                  value={search}
                  onChange={e => setSearch(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <Select
                  value={selectedNetwork}
                  onValueChange={setSelectedNetwork}
                >
                  <SelectTrigger className="text-black dark:text-white bg-white dark:bg-neutral-700">
                    <SelectValue placeholder="Selecionar rede social" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as redes</SelectItem>
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="tiktok">TikTok</SelectItem>
                    <SelectItem value="youtube">YouTube</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={orderBy}
                  onValueChange={setOrderBy}
                >
                  <SelectTrigger className="text-black dark:text-white bg-white dark:bg-neutral-700">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="views">Visualizações</SelectItem>
                    <SelectItem value="likes">Curtidas</SelectItem>
                    <SelectItem value="comments">Comentários</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Table */}
            <Card className="bg-transparent text-black dark:text-white border-none">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full border-none">
                    <thead>
                      <tr className="text-left">
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          INFLUENCER
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          REDE SOCIAL
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          LINK DA PUBLICAÇÃO
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          VISUALIZAÇÕES
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          CURTIDAS
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          COMENTÁRIOS
                        </th>
                        <th className="p-4 font-medium text-gray-600 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
                          GÊNERO
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredInfluencers.map((influencer, index) => (
                        <tr key={index} className="hover:bg-gray-50 dark:hover:bg-neutral-700">
                          <td className="p-4 border-t border-gray-100 dark:border-gray-700">
                            <div className="flex items-center gap-2">
                              {influencer.photo ? (
                                <img
                                  src={influencer.photo}
                                  alt={influencer.name}
                                  className="w-8 h-8 rounded-full object-cover bg-gray-200"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                              )}
                              <span className="font-medium">
                                {influencer.name}
                              </span>
                            </div>
                          </td>
                          <td className="p-4 border-t border-gray-100">
                            <div
                              className={`w-6 h-6 ${platformBg[influencer.platform] || 'bg-gray-300'} rounded flex items-center justify-center text-white text-xs`}
                            >
                              {platformIcons[influencer.platform] ||
                                influencer.platform}
                            </div>
                          </td>
                          <td className="p-4 border-t border-gray-100">
                            <Button
                              variant="link"
                              className="p-0 h-auto text-blue-600"
                            >
                              Ver post
                            </Button>
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100">
                            {influencer.views}
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100">
                            {influencer.likes}
                          </td>
                          <td className="p-4 font-medium border-t border-gray-100">
                            {influencer.comments}
                          </td>
                          <td className="p-4">
                        <div className="flex items-center gap-2 text-xs">
                          <div className="flex items-center gap-1">
                            <div className="w-6 h-6  bg-pink-400 rounded-xs "></div>
                            <span className=''>{influencer.womenPercent}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-6 h-6 bg-sky-400 rounded-xs"></div>
                            <span className=''>{influencer.menPercent}</span>
                          </div>
                        </div>
                      </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
          {/* Pagination */}
          <div className="flex items-center justify-between p-4 ">
            <div className="text-sm  text-gray-600">
              Mostrando de 1 a 10 de 234 perfis
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 p-0 bg-primary-500 text-black border-primary-500 hover:bg-primary-600"
              >
                1
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 p-0 bg-transparent"
              >
                2
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 p-0 bg-transparent"
              >
                3
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="w-8 h-8 p-0 bg-transparent"
              >
                4
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
